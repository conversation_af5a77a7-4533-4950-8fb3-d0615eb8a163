<?php

namespace app\admin\controller\players;

use app\common\controller\Backend;
use think\Log; // 添加 Log facade
use think\Db;
use think\exception\ValidateException;
use think\exception\PDOException;
use think\Exception;

/**
 * 玩家管理
 *
 * @icon fa fa-user
 */
class Players extends Backend
{
    protected $model = null;
    protected $searchFields = 'id,username,nickname';
    protected $noNeedRight = ['index', 'getPlayerTypeList', 'update_tag', 'add_remark', 'get_remark', 'get_vip_config', 'set_vip_level', 'unbind_setting', 'get_player_unbind_settings', 'save_player_unbind_setting', 'detail_tabs', 'direct_login', 'update_password', 'get_deposit', 'get_withdraw', 'get_commission', 'get_children', 'get_code_change', 'player_detail', 'getStatistics', 'get_game_data', 'get_code_amount'];
    protected $multiFields = 'team_withdraw_audit,personal_withdraw_audit,self_unbind_status,is_active,no_unbind_subordinate,is_game_banned';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\players\Players;
        // 获取玩家分组列表
        $playerTypeList = \app\admin\model\players\PlayerTags::getTagList();
        $this->view->assign('playerTypeList', $playerTypeList);
    }

    /**
     * 获取玩家类型列表
     */
    public function getPlayerTypeList()
    {
        $list = \app\admin\model\players\PlayerTags::getTagList(true);
        // 在最前面添加一个默认选项
        $defaultOption = ['0' => __('All')];
        $list = array_merge($defaultOption, $list);
        return json(['code' => 1, 'msg' => '', 'data' => $list]);
    }

    /**
     * 获取统计信息
     */
    public function getStatistics()
    {
        try {
            // 使用SqlFilter工具类获取WHERE子句
            $playersWhereClause = \app\admin\library\SqlFilter::getWhereClause('players');

            // 构建玩家余额总和查询
            $balanceSql = "SELECT SUM(balance) as total_sum FROM players" . $playersWhereClause;
            $gameBalanceResult = Db::query($balanceSql);
            $gameBalance = isset($gameBalanceResult[0]['total_sum']) ? $gameBalanceResult[0]['total_sum'] : 0;

            // 构建提现中金额查询
            $withdrawWhereClause = \app\admin\library\SqlFilter::getWhereClause('withdraw_orders', 'AND');
            $frozenAmountSql = "SELECT SUM(amount) as total_sum FROM withdraw_orders WHERE audit_status IN (0, 1)" . $withdrawWhereClause;
            $frozenAmountResult = Db::query($frozenAmountSql);
            $frozenAmount = isset($frozenAmountResult[0]['total_sum']) ? $frozenAmountResult[0]['total_sum'] : 0;

            // 构建总充值金额查询
            $totalDepositSql = "SELECT SUM(total_deposit) as total_sum FROM players" . $playersWhereClause;
            $totalDepositResult = Db::query($totalDepositSql);
            $totalDeposit = isset($totalDepositResult[0]['total_sum']) ? $totalDepositResult[0]['total_sum'] : 0;

            // 构建总提现金额查询
            $totalWithdrawSql = "SELECT SUM(total_withdraw) as total_sum FROM players" . $playersWhereClause;
            $totalWithdrawResult = Db::query($totalWithdrawSql);
            $totalWithdraw = isset($totalWithdrawResult[0]['total_sum']) ? $totalWithdrawResult[0]['total_sum'] : 0;

            // 确保值是数值类型而不是NULL
            $gameBalance = floatval($gameBalance);
            $frozenAmount = floatval($frozenAmount);
            $totalDeposit = floatval($totalDeposit);
            $totalWithdraw = floatval($totalWithdraw);

            $statistics = [
                'game_balance' => number_format($gameBalance, 2),
                'frozen_amount' => number_format($frozenAmount, 2),
                'total_deposit' => number_format($totalDeposit, 2),
                'total_withdraw' => number_format($totalWithdraw, 2)
            ];

            return json(['code' => 1, 'msg' => '', 'data' => $statistics]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage(), 'data' => null]);
        }
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if (false === $this->request->isAjax()) {
            return $this->view->fetch();
        }
        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        [$where, $sort, $order, , $limit] = $this->buildparams();

        // 获取筛选参数
        $filter_str = $this->request->get('filter', '');
        $filter = $filter_str ? json_decode($filter_str, true) : [];
        $channel_id = isset($filter['channel_id']) ? $filter['channel_id'] : null;

        // 初始化查询构造器
        $query = $this->model;

        // 初始化别名和字段
        $query->alias('p');

        // 如果有渠道筛选，添加条件
        if ($channel_id && $channel_id != 'all') {
            $query->where('p.channel_id', $channel_id);
        }

        // 获取当前用户角色组ID
        $roleGroupId = \app\admin\library\SqlFilter::getCurrentRoleGroupId();

        // 根据角色类型应用不同的过滤条件
        if ($roleGroupId == 4) { // 渠道角色
            $channelId = \app\admin\library\SqlFilter::getCurrentChannelId();
            if ($channelId) {
                $query->where('p.channel_id', $channelId);
            }
        } elseif ($roleGroupId == 6) { // 业务员角色
            $agentId = \app\admin\library\SqlFilter::getCurrentAgentId();
            if ($agentId) {
                $query->where('p.agent_id', $agentId);
            }
        }

        // 处理特殊排序字段
        if (isset($sort)) {
            switch ($sort) {
                case 'withdrawing_balance':
                    // 对于提现中余额，使用子查询进行排序
                    $query->field('p.*, (SELECT COALESCE(SUM(amount), 0) FROM withdraw_orders WHERE player_id = p.id AND audit_status IN (0, 1)) as withdrawing_balance_sort');
                    $sort = 'withdrawing_balance_sort';
                    break;
                case 'direct_referrals_count':
                    // 直推人数排序
                    $query->field('p.*, (SELECT COUNT(*) FROM player_relations WHERE ancestor_id = p.id AND relation_level = 1) as direct_referrals_count_sort');
                    $sort = 'direct_referrals_count_sort';
                    break;
                case 'direct_deposit_referrals_count':
                    // 直推充值人数排序
                    $query->field('p.*, (SELECT COUNT(DISTINCT pr.player_id) FROM player_relations pr JOIN deposit_orders d ON pr.player_id = d.player_id WHERE pr.ancestor_id = p.id AND pr.relation_level = 1 AND d.payment_status = 1) as direct_deposit_referrals_count_sort');
                    $sort = 'direct_deposit_referrals_count_sort';
                    break;
                case 'team_total_deposit':
                    // 团队总充值排序
                    $query->field('p.*, (SELECT COALESCE(SUM(d.amount), 0) FROM player_relations pr JOIN deposit_orders d ON pr.player_id = d.player_id WHERE pr.ancestor_id = p.id AND d.payment_status = 1) as team_total_deposit_sort');
                    $sort = 'team_total_deposit_sort';
                    break;
                case 'team_total_withdraw':
                    // 团队总提现排序
                    $query->field('p.*, (SELECT COALESCE(SUM(w.amount), 0) FROM player_relations pr JOIN withdraw_orders w ON pr.player_id = w.player_id WHERE pr.ancestor_id = p.id AND w.audit_status IN (4, 8)) as team_total_withdraw_sort');
                    $sort = 'team_total_withdraw_sort';
                    break;
                case 'team_deposit_withdraw_diff':
                    // 团队充提差排序
                    $query->field('p.*, ((SELECT COALESCE(SUM(d.amount), 0) FROM player_relations pr JOIN deposit_orders d ON pr.player_id = d.player_id WHERE pr.ancestor_id = p.id AND d.payment_status = 1) - (SELECT COALESCE(SUM(w.amount), 0) FROM player_relations pr JOIN withdraw_orders w ON pr.player_id = w.player_id WHERE pr.ancestor_id = p.id AND w.audit_status IN (4, 8))) as team_deposit_withdraw_diff_sort');
                    $sort = 'team_deposit_withdraw_diff_sort';
                    break;
                default:
                    // 其他字段添加表前缀
                    $sort = "p.$sort";
                    break;
            }
        }

        // 应用原始的where条件
        $query->where($where);

        // 执行查询
        $list = $query->order($sort, $order)->paginate($limit);
        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post('row/a');
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                // 处理 referrer_id 为空的情况
                if (empty($params['referrer_id'])) {
                    $params['referrer_id'] = null;
                }
                $result = false;
                Db::startTrans();
                try {
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        // 获取标签列表
        $playerTagsList = \app\admin\model\players\PlayerTags::getTagList();
        $this->view->assign('playerTagsList', $playerTagsList);
        // 获取业务员列表
        $agentList = \app\admin\model\agents_config\Agents::getAgentList(true);
        $this->view->assign('agentList', $agentList);
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post('row/a');
            if ($params) {
                $params = $this->preExcludeFields($params);
                // 处理 referrer_id 为空的情况
                if (empty($params['referrer_id'])) {
                    $params['referrer_id'] = null;
                }
                $result = false;
                Db::startTrans();
                try {
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        // 获取标签列表
        $playerTagsList = \app\admin\model\players\PlayerTags::getTagList();
        $this->view->assign('playerTagsList', $playerTagsList);
        // 获取业务员列表
        $agentList = \app\admin\model\agents_config\Agents::getAgentList(true);
        $this->view->assign('agentList', $agentList);
        $this->view->assign('row', $row);
        return $this->view->fetch();
    }

    /**
     * 删除
     */
    public function del($ids = '')
    {
        if ($ids) {
            $pk = $this->model->getPk();
            $adminIds = $this->getDataLimitAdminIds();
            if (is_array($adminIds)) {
                $this->model->where($this->dataLimitField, 'in', $adminIds);
            }
            $list = $this->model->where($pk, 'in', $ids)->select();
            $count = 0;
            Db::startTrans();
            try {
                foreach ($list as $item) {
                    $count += $item->delete();
                }
                Db::commit();
            } catch (PDOException|Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($count) {
                $this->success();
            } else {
                $this->error(__('Parameter %s can not be empty', 'ids'));
            }
        }
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }

    /**
     * 添加玩家备注
     */
    public function add_remark()
    {
        $player_id = $this->request->post('player_id');
        $content = $this->request->post('content');

        if (!$player_id || !$content) {
            $this->error(__('Parameter %s can not be empty', 'player_id/content'));
        }

        // 检查玩家是否存在
        $player = $this->model->find($player_id);
        if (!$player) {
            $this->error(__('Player does not exist'));
        }

        // 添加备注记录
        $result = Db::table('player_remarks')->insert([
            'player_id' => $player_id,
            'admin_id' => $this->auth->id,
            'content' => $content,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            $this->success(__('Remark added successfully'));
        } else {
            $this->error(__('Operation failed'));
        }
    }

    /**
     * 获取VIP等级配置
     */
    public function get_vip_config()
    {
        // 查询VIP等级配置
        $vipConfig = Db::table('vip_config')
            ->order('level', 'asc')
            ->select();

        // 将结果转换为以level为键的关联数组
        $result = [];
        foreach ($vipConfig as $config) {
            $result[$config['level']] = $config;
        }

        // 使用success方法返回结果
        $this->success('', null, $result);
    }

    /**
     * 设置VIP等级
     */
    public function set_vip_level()
    {
        $player_id = $this->request->post('player_id');
        $vip_level = $this->request->post('vip_level');

        if (!$player_id || !isset($vip_level)) {
            $this->error(__('Parameter %s can not be empty', 'player_id/vip_level'));
        }

        // 检查玩家是否存在
        $player = $this->model->find($player_id);
        if (!$player) {
            $this->error(__('Player does not exist'));
        }

        // 检查VIP等级是否存在
        $vipConfig = Db::table('vip_config')->where('level', $vip_level)->find();
        if (!$vipConfig) {
            $this->error(__('Invalid VIP level'));
        }

        // 更新玩家VIP等级
        $result = $this->model->where('id', $player_id)->update(['vip_level' => $vip_level]);

        if ($result) {
            $this->success(__('VIP level updated successfully'));
        } else {
            $this->error(__('Failed to update VIP level'));
        }
    }

    /**
     * 获取玩家备注
     */
    public function get_remark()
    {
        $player_id = $this->request->get('player_id');

        if (!$player_id) {
            $this->error(__('Parameter %s can not be empty', 'player_id'));
        }

        // 查询玩家的备注记录
        $remarks = Db::table('player_remarks')
            ->alias('pr')
            ->join('admin a', 'pr.admin_id = a.id', 'LEFT')
            ->where('pr.player_id', $player_id)
            ->field('pr.*, a.username as operator, pr.admin_id as operator_id, pr.content as remark, pr.created_at as operation_time')
            ->order('pr.created_at', 'desc')
            ->select();

        $result = ['total' => count($remarks), 'rows' => $remarks];
        return json($result);
    }

    /**
     * 解绑提现账户
     */
    public function unbind_withdraw_account()
    {
        $player_id = $this->request->post('player_id');
        if (!$player_id) {
            $this->error(__('Parameter %s can not be empty', 'player_id'));
        }

        // 使用事务同时删除提现账户和清除提现密码
        Db::startTrans();
        try {
            // 删除玩家的提现账户记录
            $deleteResult = Db::table('player_withdraw_accounts')
                ->where('player_id', $player_id)
                ->delete();

            // 清除玩家的提现密码
            $updateResult = Db::table('players')
                ->where('id', $player_id)
                ->update(['withdraw_password' => '']);

            // 如果至少有一个操作成功，则提交事务
            if ($deleteResult || $updateResult) {
                Db::commit();
                $this->success(__('Withdrawal account unbound and password cleared successfully'));
            } else {
                Db::rollback();
                $this->error(__('No withdrawal account found or failed to unbind'));
            }
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }

    /**
     * 全局掉绑设置
     */
    public function unbind_setting()
    {
        return $this->view->fetch();
    }

    /**
     * 获取玩家掉绑设置
     */
    public function get_player_unbind_settings()
    {
        $player_id = $this->request->param('player_id');
        if ($player_id === null || $player_id === '') {
            $this->error(__('Parameter error'));
        }

        // 获取玩家掉绑设置
        $settings = Db::table('player_unbind_settings')->where('player_id', $player_id)->find();

        // 如果没有设置，返回默认设置
        if (!$settings) {
            // 默认设置
            $settings = [
                'unbind_mode' => 0,
                'reg_unbind_type' => 0,
                'deposit_unbind_type' => 0,
                'reg_unbind_threshold' => 0,
                'reg_unbind_interval' => 0,
                'reg_unbind_prob' => 0,
                'deposit_unbind_threshold' => 0,
                'deposit_unbind_interval' => 0,
                'deposit_unbind_prob' => 0
            ];
        }

        return json(['code' => 1, 'msg' => '', 'settings' => $settings]);
    }

    /**
     * 保存玩家掉绑设置
     */
    public function save_player_unbind_setting()
    {
        $params = $this->request->post();

        if (!isset($params['player_id']) || $params['player_id'] === null || $params['player_id'] === '') {
            $this->error(__('Parameter error'));
        }

        // 将前端参数转换为数据库格式
        $data = [
            'player_id' => intval($params['player_id']),
            'unbind_mode' => intval($params['unbind_mode']),
            'reg_unbind_type' => intval($params['reg_unbind_type']),
            'deposit_unbind_type' => intval($params['deposit_unbind_type']),
            'reg_unbind_threshold' => intval($params['reg_unbind_threshold']),
            'deposit_unbind_threshold' => intval($params['deposit_unbind_threshold']),
            'reg_unbind_interval' => intval($params['reg_unbind_interval']),
            'reg_unbind_prob' => intval($params['reg_unbind_prob']),
            'deposit_unbind_interval' => intval($params['deposit_unbind_interval']),
            'deposit_unbind_prob' => intval($params['deposit_unbind_prob']),
            'sync_next' => isset($params['sync_next']) && $params['sync_next'] == 1 ? 1 : 0
        ];



        // 检查是否需要同步到下级
        $syncCount = 0;

        // 设置同步到下级标志
        $data['sync_next'] = isset($params['sync_next']) && $params['sync_next'] == 1 ? 1 : 0;

        // 检查是否存在玩家设置记录
        $settings = Db::table('player_unbind_settings')->where('player_id', $data['player_id'])->find();

        $result = false;
        if ($settings) {
            // 更新现有记录
            $result = Db::table('player_unbind_settings')->where('player_id', $data['player_id'])->update($data);
        } else {
            // 创建新记录
            $result = Db::table('player_unbind_settings')->insert($data);
        }

        // 如果需要同步到下级，且不是全局设置
        if ($data['sync_next'] == 1 && $data['player_id'] > 0) {
            // 获取该玩家的所有下级
            $subordinates = Db::table('player_relations')
                ->where('ancestor_id', $data['player_id'])
                ->column('player_id'); // 不限制层级，同步所有下级

            if (!empty($subordinates)) {
                // 移除player_id字段，准备批量插入或更新
                $subordinateData = $data;
                unset($subordinateData['player_id']);
                // 设置下级玩家的sync_next为0
                $subordinateData['sync_next'] = 0;

                foreach ($subordinates as $subordinateId) {
                    // 检查下级是否已有设置
                    $subordinateSettings = Db::table('player_unbind_settings')
                        ->where('player_id', $subordinateId)
                        ->find();

                    if ($subordinateSettings) {
                        $subordinateData['player_id'] = $subordinateId;
                        // 更新现有记录
                        Db::table('player_unbind_settings')
                            ->where('player_id', $subordinateId)
                            ->update($subordinateData);
                    } else {
                        // 创建新记录
                        $newData = $subordinateData;
                        $newData['player_id'] = $subordinateId;
                        Db::table('player_unbind_settings')->insert($newData);
                    }
                    $syncCount++;
                }
            }
        }

        // 返回JSON格式的响应
        if ($result) {
            $msg = __('Save successful');
            if ($syncCount > 0) {
                $msg .= ', ' . __('Synced to %d subordinates', $syncCount);
            }

            return json([
                'code' => 1,
                'msg' => $msg,
                'data' => [
                    'player_id' => $params['player_id'],
                    'sync_count' => $syncCount,
                    // 如果需要重定向，可以添加URL
                    'url' => $params['player_id'] == 0 ? '' : url('players/players/player_detail', ['ids' => $params['player_id']])
                ]
            ]);
        } else {
            return json([
                'code' => 0,
                'msg' => __('Save failed'),
                'data' => null
            ]);
        }
    }

    /**
     * 加载标签页内容
     *
     * @param string $name 标签页名称
     * @return string
     */
    public function detail_tabs($name = '')
    {
        if (empty($name)) {
            // 尝试从URL中获取标签页名称
            $pathinfo = $this->request->pathinfo();
            $parts = explode('/', $pathinfo);
            $name = end($parts);
        }

        if (empty($name)) {
            $this->error('参数错误');
        }

        // 获取玩家ID
        $player_id = $this->request->get('player_id');
        if (!$player_id) {
            $this->error('参数错误：缺少玩家ID');
        }

        // 检查玩家是否存在
        $player = $this->model->find($player_id);
        if (!$player) {
            $this->error('玩家不存在');
        }

        // 检查标签页文件是否存在
        $viewFile = 'players/players/detail_tabs/' . $name;
        if (!is_file(APP_PATH . 'admin/view/' . $viewFile . '.html')) {
            $this->error('标签页不存在');
        }

        // 将玩家ID传递给视图
        $this->view->assign('player_id', $player_id);
        $this->view->assign('row', $player);

        // 获取玩家提款绑定信息
        $withdrawAccount = Db::table('player_withdraw_accounts')
            ->where('player_id', $player_id)
            ->find();
        $this->view->assign('withdraw_account', $withdrawAccount);

        // 加载通用数据

        // 获取玩家所属渠道信息（用于基本信息标签页）
        if ($name == 'basic' && $player->agent_id) {
            $agent = Db::table('agents')->where('id', $player->agent_id)->find();
            if ($agent && isset($agent['channel_id'])) {
                $channel = Db::table('channels')->where('id', $agent['channel_id'])->find();
                if ($channel) {
                    $player->channel_id = $channel['id'];
                    // 直接使用渠道名称
                    $player->channel_name = $channel['name'] . '(' . $channel['id'] . ')';
                }
            }
        }

        // 如果是同IP查询标签页，直接查询数据
        if ($name == 'same_ip') {
            try {
                // 查询player_login_logs表中login_ip有重复的记录
                // 首先找出所有有重复IP的登录记录（被不同玩家使用的IP）
                $duplicateIps = Db::table('player_login_logs')
                    ->group('login_ip')
                    ->having('COUNT(DISTINCT player_id) > 1') // 确保IP被不同玩家使用
                    ->column('login_ip');

                // 获取当前玩家使用过的IP
                $playerIps = Db::table('player_login_logs')
                    ->where('player_id', $player_id)
                    ->column('login_ip');

                // 找出当前玩家使用过的且被其他玩家也使用过的IP
                $commonIps = array_intersect($playerIps, $duplicateIps);

                // 查询使用过相同IP登录的其他玩家
                $sameIpPlayers = [];
                if (!empty($commonIps)) {
                    $query = Db::table('player_login_logs')
                        ->alias('l')
                        ->join(['players'=>'p'], 'l.player_id = p.id')
                        ->where('l.login_ip', 'in', $commonIps)
                        ->where('l.player_id', '<>', $player_id); // 排除当前玩家

                    // 获取当前用户角色组ID
                    $roleGroupId = \app\admin\library\SqlFilter::getCurrentRoleGroupId();

                    // 根据角色类型应用不同的过滤条件
                    if ($roleGroupId == 4) { // 渠道角色
                        $channelId = \app\admin\library\SqlFilter::getCurrentChannelId();
                        if ($channelId) {
                            $query->where('p.channel_id', $channelId);
                        }
                    } elseif ($roleGroupId == 6) { // 业务员角色
                        $agentId = \app\admin\library\SqlFilter::getCurrentAgentId();
                        if ($agentId) {
                            $query->where('p.agent_id', $agentId);
                        }
                    }

                    $sameIpPlayers = $query->group('l.player_id') // 按玩家ID分组，避免重复
                        ->field('p.id, p.username, p.register_ip, p.created_at as register_time, p.last_login_ip, p.last_login_time,
                                MAX(l.created_at) as last_log_time, l.login_ip')
                        ->order('last_log_time DESC')
                        ->select();
                }

                // 将数据分配给视图
                $this->view->assign('sameIpPlayers', $sameIpPlayers);

            } catch (\Exception $e) {
                // 记录错误日志
                Log::error('获取同IP玩家列表失败: ' . $e->getMessage());
                // 分配空数据
                $this->view->assign('sameIpPlayers', []);
            }
        }

        // 如果是备注标签页，直接查询数据
        if ($name == 'remark') {
            // 查询玩家的备注记录
            $remarks = Db::table('player_remarks')
                ->alias('pr')
                ->join('admin a', 'pr.admin_id = a.id', 'LEFT')
                ->where('pr.player_id', $player_id)
                ->field('pr.*, a.username as operator, pr.admin_id as operator_id, pr.content as remark, pr.created_at as operation_time')
                ->order('pr.created_at', 'desc')
                ->select();

            // 将数据分配给视图
            $this->view->assign('remarks', $remarks);
        }

        // 如果是充值分成设置标签页，获取玩家佣金配置
        if ($name == 'commission_configs') {
            // 获取玩家佣金配置
            $config = \app\admin\model\commission\PlayerCommissionConfigs::getPlayerConfig($player_id);

            // 将数据分配给视图
            $this->view->assign('config', $config);
        }

        // 如果是下级数据标签页，直接查询数据
        if ($name == 'children') {
            // 获取当前用户角色组ID
            $roleGroupId = \app\admin\library\SqlFilter::getCurrentRoleGroupId();

            // 根据角色类型应用不同的过滤条件
            $whereCondition = [];
            if ($roleGroupId == 4) { // 渠道角色
                $channelId = \app\admin\library\SqlFilter::getCurrentChannelId();
                if ($channelId) {
                    $whereCondition['p.channel_id'] = $channelId;
                }
            } elseif ($roleGroupId == 6) { // 业务员角色
                $agentId = \app\admin\library\SqlFilter::getCurrentAgentId();
                if ($agentId) {
                    $whereCondition['p.agent_id'] = $agentId;
                }
            }

            // 查询一级下线数量
            $level1Count = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 1)
                ->where($whereCondition)
                ->count();

            // 查询二级下线数量
            $level2Count = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 2)
                ->where($whereCondition)
                ->count();

            // 查询三级下线数量
            $level3Count = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 3)
                ->where($whereCondition)
                ->count();

            // 计算总下线数量
            $totalCount = $level1Count + $level2Count + $level3Count;

            // 邀请奖励人数查询已移除

            // 查询各级别的宝箱奖励人数
            $level1TreasureBoxCount = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['player_balance_logs' => 'pbl'], 'pr.player_id = pbl.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 1)
                ->where('pbl.transaction_type', 12) // 代理宝箱奖励类型
                ->where($whereCondition)
                ->group('pr.player_id')
                ->count();

            $level2TreasureBoxCount = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['player_balance_logs' => 'pbl'], 'pr.player_id = pbl.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 2)
                ->where('pbl.transaction_type', 12) // 代理宝箱奖励类型
                ->where($whereCondition)
                ->group('pr.player_id')
                ->count();

            $level3TreasureBoxCount = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['player_balance_logs' => 'pbl'], 'pr.player_id = pbl.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 3)
                ->where('pbl.transaction_type', 12) // 代理宝箱奖励类型
                ->where($whereCondition)
                ->group('pr.player_id')
                ->count();

            // 查询各级别的充值人数和金额
            $level1DepositData = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['deposit_orders' => 'd'], 'pr.player_id = d.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 1)
                ->where('d.payment_status', 1) // 已支付的订单
                ->where($whereCondition)
                ->field('COUNT(DISTINCT pr.player_id) as deposit_count, SUM(d.amount) as deposit_amount')
                ->find();

            $level2DepositData = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['deposit_orders' => 'd'], 'pr.player_id = d.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 2)
                ->where('d.payment_status', 1) // 已支付的订单
                ->where($whereCondition)
                ->field('COUNT(DISTINCT pr.player_id) as deposit_count, SUM(d.amount) as deposit_amount')
                ->find();

            $level3DepositData = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['deposit_orders' => 'd'], 'pr.player_id = d.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 3)
                ->where('d.payment_status', 1) // 已支付的订单
                ->where($whereCondition)
                ->field('COUNT(DISTINCT pr.player_id) as deposit_count, SUM(d.amount) as deposit_amount')
                ->find();

            // 查询各级别的提现人数和金额
            $level1WithdrawData = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['withdraw_orders' => 'w'], 'pr.player_id = w.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 1)
                ->where('w.audit_status', 'in', [4, 8]) // 已完成或虚拟支付的订单
                ->where($whereCondition)
                ->field('COUNT(DISTINCT pr.player_id) as withdraw_count, SUM(w.amount) as withdraw_amount')
                ->find();

            $level2WithdrawData = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['withdraw_orders' => 'w'], 'pr.player_id = w.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 2)
                ->where('w.audit_status', 'in', [4, 8]) // 已完成或虚拟支付的订单
                ->where($whereCondition)
                ->field('COUNT(DISTINCT pr.player_id) as withdraw_count, SUM(w.amount) as withdraw_amount')
                ->find();

            $level3WithdrawData = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['withdraw_orders' => 'w'], 'pr.player_id = w.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 3)
                ->where('w.audit_status', 'in', [4, 8]) // 已完成或虚拟支付的订单
                ->where($whereCondition)
                ->field('COUNT(DISTINCT pr.player_id) as withdraw_count, SUM(w.amount) as withdraw_amount')
                ->find();

            // 计算各级别的平均充值
            $level1AvgDeposit = $level1DepositData['deposit_count'] > 0 ?
                $level1DepositData['deposit_amount'] / $level1DepositData['deposit_count'] : 0;
            $level2AvgDeposit = $level2DepositData['deposit_count'] > 0 ?
                $level2DepositData['deposit_amount'] / $level2DepositData['deposit_count'] : 0;
            $level3AvgDeposit = $level3DepositData['deposit_count'] > 0 ?
                $level3DepositData['deposit_amount'] / $level3DepositData['deposit_count'] : 0;

            // 计算各级别的盈亏
            $level1Profit = $level1DepositData['deposit_amount'] - $level1WithdrawData['withdraw_amount'];
            $level2Profit = $level2DepositData['deposit_amount'] - $level2WithdrawData['withdraw_amount'];
            $level3Profit = $level3DepositData['deposit_amount'] - $level3WithdrawData['withdraw_amount'];

            // 计算总计数据
            $totalPeople = $level1Count + $level2Count + $level3Count;
            // 邀请奖励人数计算已移除
            $totalTreasureBoxPeople = $level1TreasureBoxCount + $level2TreasureBoxCount + $level3TreasureBoxCount;
            $totalDepositPeople = $level1DepositData['deposit_count'] + $level2DepositData['deposit_count'] + $level3DepositData['deposit_count'];
            $totalDepositAmount = $level1DepositData['deposit_amount'] + $level2DepositData['deposit_amount'] + $level3DepositData['deposit_amount'];
            $totalAvgDeposit = $totalDepositPeople > 0 ? $totalDepositAmount / $totalDepositPeople : 0;
            $totalWithdrawPeople = $level1WithdrawData['withdraw_count'] + $level2WithdrawData['withdraw_count'] + $level3WithdrawData['withdraw_count'];
            $totalWithdrawAmount = $level1WithdrawData['withdraw_amount'] + $level2WithdrawData['withdraw_amount'] + $level3WithdrawData['withdraw_amount'];
            $totalProfit = $totalDepositAmount - $totalWithdrawAmount;

            // 组装数据
            $childrenData = [
                'level1' => [
                    'total_people' => $level1Count,
                    // 邀请奖励人数字段已移除
                    'treasure_box_people' => $level1TreasureBoxCount,
                    'deposit_people' => $level1DepositData['deposit_count'],
                    'deposit_amount' => $level1DepositData['deposit_amount'],
                    'avg_deposit' => $level1AvgDeposit,
                    'withdraw_people' => $level1WithdrawData['withdraw_count'],
                    'withdraw_amount' => $level1WithdrawData['withdraw_amount'],
                    'profit' => $level1Profit
                ],
                'level2' => [
                    'total_people' => $level2Count,
                    // 邀请奖励人数字段已移除
                    'treasure_box_people' => $level2TreasureBoxCount,
                    'deposit_people' => $level2DepositData['deposit_count'],
                    'deposit_amount' => $level2DepositData['deposit_amount'],
                    'avg_deposit' => $level2AvgDeposit,
                    'withdraw_people' => $level2WithdrawData['withdraw_count'],
                    'withdraw_amount' => $level2WithdrawData['withdraw_amount'],
                    'profit' => $level2Profit
                ],
                'level3' => [
                    'total_people' => $level3Count,
                    // 邀请奖励人数字段已移除
                    'treasure_box_people' => $level3TreasureBoxCount,
                    'deposit_people' => $level3DepositData['deposit_count'],
                    'deposit_amount' => $level3DepositData['deposit_amount'],
                    'avg_deposit' => $level3AvgDeposit,
                    'withdraw_people' => $level3WithdrawData['withdraw_count'],
                    'withdraw_amount' => $level3WithdrawData['withdraw_amount'],
                    'profit' => $level3Profit
                ],
                'total' => [
                    'total_people' => $totalPeople,
                    // 邀请奖励人数字段已移除
                    'treasure_box_people' => $totalTreasureBoxPeople,
                    'deposit_people' => $totalDepositPeople,
                    'deposit_amount' => $totalDepositAmount,
                    'avg_deposit' => $totalAvgDeposit,
                    'withdraw_people' => $totalWithdrawPeople,
                    'withdraw_amount' => $totalWithdrawAmount,
                    'profit' => $totalProfit
                ]
            ];

            // 将数据分配给视图
            $this->view->assign('childrenData', $childrenData);

            // 查询虚拟数据(Faked)
            // 查询一级虚拟数据下线数量
            $level1FakedCount = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 1)
                ->where('p.self_unbind_status', 0) // 虚拟数据(Faked)
                ->where($whereCondition)
                ->count();

            // 查询二级掉绑下线数量
            $level2FakedCount = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 2)
                ->where('p.self_unbind_status', 0) // 虚拟数据(Faked)
                ->where($whereCondition)
                ->count();

            // 查询三级掉绑下线数量
            $level3FakedCount = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 3)
                ->where('p.self_unbind_status', 0) // 虚拟数据(Faked)
                ->where($whereCondition)
                ->count();

            // 虚拟数据邀请奖励人数查询已移除

            // 查询各级别的虚拟数据宝箱奖励人数
            $level1FakedTreasureBoxCount = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['player_balance_logs' => 'pbl'], 'pr.player_id = pbl.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 1)
                ->where('p.self_unbind_status', 0) // 虚拟数据(Faked)
                ->where('pbl.transaction_type', 12) // 代理宝箱奖励类型
                ->where($whereCondition)
                ->group('pr.player_id')
                ->count();

            $level2FakedTreasureBoxCount = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['player_balance_logs' => 'pbl'], 'pr.player_id = pbl.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 2)
                ->where('p.self_unbind_status', 0) // 虚拟数据(Faked)
                ->where('pbl.transaction_type', 12) // 代理宝箱奖励类型
                ->where($whereCondition)
                ->group('pr.player_id')
                ->count();

            $level3FakedTreasureBoxCount = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['player_balance_logs' => 'pbl'], 'pr.player_id = pbl.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 3)
                ->where('p.self_unbind_status', 0) // 虚拟数据(Faked)
                ->where('pbl.transaction_type', 12) // 代理宝箱奖励类型
                ->where($whereCondition)
                ->group('pr.player_id')
                ->count();

            // 查询各级别的虚拟数据充值人数和金额
            $level1FakedDepositData = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['deposit_orders' => 'd'], 'pr.player_id = d.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 1)
                ->where('p.self_unbind_status', 0) // 虚拟数据(Faked)
                ->where('d.payment_status', 1) // 已支付的订单
                ->where($whereCondition)
                ->field('COUNT(DISTINCT pr.player_id) as deposit_count, SUM(d.amount) as deposit_amount')
                ->find();

            $level2FakedDepositData = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['deposit_orders' => 'd'], 'pr.player_id = d.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 2)
                ->where('p.self_unbind_status', 0) // 虚拟数据(Faked)
                ->where('d.payment_status', 1) // 已支付的订单
                ->where($whereCondition)
                ->field('COUNT(DISTINCT pr.player_id) as deposit_count, SUM(d.amount) as deposit_amount')
                ->find();

            $level3FakedDepositData = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['deposit_orders' => 'd'], 'pr.player_id = d.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 3)
                ->where('p.self_unbind_status', 0) // 虚拟数据(Faked)
                ->where('d.payment_status', 1) // 已支付的订单
                ->where($whereCondition)
                ->field('COUNT(DISTINCT pr.player_id) as deposit_count, SUM(d.amount) as deposit_amount')
                ->find();

            // 查询各级别的虚拟数据提现人数和金额
            $level1FakedWithdrawData = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['withdraw_orders' => 'w'], 'pr.player_id = w.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 1)
                ->where('p.self_unbind_status', 0) // 虚拟数据(Faked)
                ->where('w.audit_status', 'in', [4, 8]) // 已完成或虚拟支付的订单
                ->where($whereCondition)
                ->field('COUNT(DISTINCT pr.player_id) as withdraw_count, SUM(w.amount) as withdraw_amount')
                ->find();

            $level2FakedWithdrawData = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['withdraw_orders' => 'w'], 'pr.player_id = w.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 2)
                ->where('p.self_unbind_status', 0) // 虚拟数据(Faked)
                ->where('w.audit_status', 'in', [4, 8]) // 已完成或虚拟支付的订单
                ->where($whereCondition)
                ->field('COUNT(DISTINCT pr.player_id) as withdraw_count, SUM(w.amount) as withdraw_amount')
                ->find();

            $level3FakedWithdrawData = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['withdraw_orders' => 'w'], 'pr.player_id = w.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 3)
                ->where('p.self_unbind_status', 0) // 虚拟数据(Faked)
                ->where('w.audit_status', 'in', [4, 8]) // 已完成或虚拟支付的订单
                ->where($whereCondition)
                ->field('COUNT(DISTINCT pr.player_id) as withdraw_count, SUM(w.amount) as withdraw_amount')
                ->find();

            // 计算各级别的虚拟数据平均充值
            $level1FakedAvgDeposit = $level1FakedDepositData['deposit_count'] > 0 ?
                $level1FakedDepositData['deposit_amount'] / $level1FakedDepositData['deposit_count'] : 0;
            $level2FakedAvgDeposit = $level2FakedDepositData['deposit_count'] > 0 ?
                $level2FakedDepositData['deposit_amount'] / $level2FakedDepositData['deposit_count'] : 0;
            $level3FakedAvgDeposit = $level3FakedDepositData['deposit_count'] > 0 ?
                $level3FakedDepositData['deposit_amount'] / $level3FakedDepositData['deposit_count'] : 0;

            // 计算各级别的虚拟数据盈亏
            $level1FakedProfit = $level1FakedDepositData['deposit_amount'] - $level1FakedWithdrawData['withdraw_amount'];
            $level2FakedProfit = $level2FakedDepositData['deposit_amount'] - $level2FakedWithdrawData['withdraw_amount'];
            $level3FakedProfit = $level3FakedDepositData['deposit_amount'] - $level3FakedWithdrawData['withdraw_amount'];

            // 计算虚拟数据总计数据
            $totalFakedPeople = $level1FakedCount + $level2FakedCount + $level3FakedCount;
            // 虚拟数据邀请奖励人数计算已移除
            $totalFakedTreasureBoxPeople = $level1FakedTreasureBoxCount + $level2FakedTreasureBoxCount + $level3FakedTreasureBoxCount;
            $totalFakedDepositPeople = $level1FakedDepositData['deposit_count'] + $level2FakedDepositData['deposit_count'] + $level3FakedDepositData['deposit_count'];
            $totalFakedDepositAmount = $level1FakedDepositData['deposit_amount'] + $level2FakedDepositData['deposit_amount'] + $level3FakedDepositData['deposit_amount'];
            $totalFakedAvgDeposit = $totalFakedDepositPeople > 0 ? $totalFakedDepositAmount / $totalFakedDepositPeople : 0;
            $totalFakedWithdrawPeople = $level1FakedWithdrawData['withdraw_count'] + $level2FakedWithdrawData['withdraw_count'] + $level3FakedWithdrawData['withdraw_count'];
            $totalFakedWithdrawAmount = $level1FakedWithdrawData['withdraw_amount'] + $level2FakedWithdrawData['withdraw_amount'] + $level3FakedWithdrawData['withdraw_amount'];
            $totalFakedProfit = $totalFakedDepositAmount - $totalFakedWithdrawAmount;

            // 组装虚拟数据
            $unbindChildrenData = [
                'level1' => [
                    'total_people' => $level1FakedCount,
                    // 邀请奖励人数字段已移除
                    'treasure_box_people' => $level1FakedTreasureBoxCount,
                    'deposit_people' => $level1FakedDepositData['deposit_count'],
                    'deposit_amount' => $level1FakedDepositData['deposit_amount'],
                    'avg_deposit' => $level1FakedAvgDeposit,
                    'withdraw_people' => $level1FakedWithdrawData['withdraw_count'],
                    'withdraw_amount' => $level1FakedWithdrawData['withdraw_amount'],
                    'profit' => $level1FakedProfit
                ],
                'level2' => [
                    'total_people' => $level2FakedCount,
                    // 邀请奖励人数字段已移除
                    'treasure_box_people' => $level2FakedTreasureBoxCount,
                    'deposit_people' => $level2FakedDepositData['deposit_count'],
                    'deposit_amount' => $level2FakedDepositData['deposit_amount'],
                    'avg_deposit' => $level2FakedAvgDeposit,
                    'withdraw_people' => $level2FakedWithdrawData['withdraw_count'],
                    'withdraw_amount' => $level2FakedWithdrawData['withdraw_amount'],
                    'profit' => $level2FakedProfit
                ],
                'level3' => [
                    'total_people' => $level3FakedCount,
                    // 邀请奖励人数字段已移除
                    'treasure_box_people' => $level3FakedTreasureBoxCount,
                    'deposit_people' => $level3FakedDepositData['deposit_count'],
                    'deposit_amount' => $level3FakedDepositData['deposit_amount'],
                    'avg_deposit' => $level3FakedAvgDeposit,
                    'withdraw_people' => $level3FakedWithdrawData['withdraw_count'],
                    'withdraw_amount' => $level3FakedWithdrawData['withdraw_amount'],
                    'profit' => $level3FakedProfit
                ],
                'total' => [
                    'total_people' => $totalFakedPeople,
                    // 邀请奖励人数字段已移除
                    'treasure_box_people' => $totalFakedTreasureBoxPeople,
                    'deposit_people' => $totalFakedDepositPeople,
                    'deposit_amount' => $totalFakedDepositAmount,
                    'avg_deposit' => $totalFakedAvgDeposit,
                    'withdraw_people' => $totalFakedWithdrawPeople,
                    'withdraw_amount' => $totalFakedWithdrawAmount,
                    'profit' => $totalFakedProfit
                ]
            ];

            // 将虚拟数据分配给视图
            $this->view->assign('fakedChildrenData', $unbindChildrenData);

            // 查询所有下级玩家的详细信息
            $subordinatePlayers = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', '<=', 3) // 最多查询三级下线
                ->where($whereCondition)
                ->field([
                    'p.id',
                    'p.phone_number',
                    'pr.relation_level',
                    'p.vip_level',
                    'p.total_deposit',
                    'p.total_withdraw',
                    'p.balance',
                    'p.total_bet',
                    'p.created_at',
                    'p.last_login_time'
                ])
                ->order('pr.relation_level ASC, p.created_at DESC')
                ->select();

            // 查询每个下级玩家的首充金额
            foreach ($subordinatePlayers as &$player) {
                // 查询首充金额
                $firstDeposit = Db::table('deposit_orders')
                    ->where('player_id', $player['id'])
                    ->where('payment_status', 1) // 已支付的订单
                    ->order('created_at ASC')
                    ->field('amount')
                    ->find();

                $player['first_deposit'] = $firstDeposit ? $firstDeposit['amount'] : 0;

                // 查询是否有宝箱奖励
                $hasTreasureBox = Db::table('player_balance_logs')
                    ->where('player_id', $player['id'])
                    ->where('transaction_type', 12) // 代理宝箱奖励类型
                    ->count() > 0;

                $player['has_treasure_box'] = $hasTreasureBox ? 1 : 0;

                // 查询直推人数（一级下线数量）
                $directReferralsCount = Db::table('player_relations')
                    ->where('ancestor_id', $player['id'])
                    ->where('relation_level', 1)
                    ->count();

                $player['direct_referrals_count'] = $directReferralsCount;
            }

            // 将下级玩家数据分配给视图
            $this->view->assign('subordinatePlayers', $subordinatePlayers);
        }

        // 如果是金币日志标签页，获取交易类型列表和数据
        if ($name == 'coin_log') {
            // 获取余额变动类型列表
            $transactionTypeList = \app\admin\model\players\BalanceChangeTypes::column('name', 'id');
            $this->view->assign('transactionTypeList', $transactionTypeList);

            // 获取玩家统计数据
            try {
                // 获取GM上分总额
                $gmIncreaseTotal = Db::table('gm_balance_adjustments')
                    ->where('player_id', $player_id)
                    ->where('adjustment_type', 1) // 上分
                    ->where('status', 1) // 已审核
                    ->sum('amount');

                // 获取GM下分总额
                $gmDecreaseTotal = Db::table('gm_balance_adjustments')
                    ->where('player_id', $player_id)
                    ->where('adjustment_type', 2) // 下分
                    ->where('status', 1) // 已审核
                    ->sum('amount');

                // 从players表中直接获取总充值和总提现金额
                $playerData = Db::table('players')
                    ->where('id', $player_id)
                    ->field('total_deposit, total_withdraw')
                    ->find();

                $totalDeposit = $playerData ? $playerData['total_deposit'] : 0;
                $totalWithdraw = $playerData ? $playerData['total_withdraw'] : 0;

                // 将数据分配给视图
                $this->view->assign('gmIncreaseTotal', $gmIncreaseTotal ?: '0.00');
                $this->view->assign('gmDecreaseTotal', $gmDecreaseTotal ?: '0.00');
                $this->view->assign('totalDeposit', $totalDeposit ?: '0.00');
                $this->view->assign('totalWithdraw', $totalWithdraw ?: '0.00');
            } catch (\Exception $e) {
                // 记录错误日志
                Log::error('获取玩家统计数据失败: ' . $e->getMessage());
                // 分配默认值
                $this->view->assign('gmIncreaseTotal', '0.00');
                $this->view->assign('gmDecreaseTotal', '0.00');
                $this->view->assign('totalDeposit', '0.00');
                $this->view->assign('totalWithdraw', '0.00');
            }

            // 获取筛选参数
            $transaction_type = $this->request->get('transaction_type', '');

            try {
                // 构建查询
                $query = Db::table('player_balance_logs')
                    ->alias('pbl')
                    ->join(['player_balance_change_types' => 'pbct'], 'pbl.transaction_type = pbct.id', 'LEFT')
                    ->where('pbl.player_id', $player_id);

                // 应用筛选条件
                if (!empty($transaction_type)) {
                    $query->where('pbl.transaction_type', $transaction_type);
                }

                // 获取当前用户角色组ID
                $roleGroupId = \app\admin\library\SqlFilter::getCurrentRoleGroupId();

                // 根据角色类型应用不同的过滤条件
                if ($roleGroupId == 4 || $roleGroupId == 6) {
                    // 关联players表以便过滤
                    $query->join(['players'=>'p'], 'pbl.player_id = p.id', 'LEFT');

                    if ($roleGroupId == 4) { // 渠道角色
                        $channelId = \app\admin\library\SqlFilter::getCurrentChannelId();
                        if ($channelId) {
                            $query->where('p.channel_id', $channelId);
                        }
                    } elseif ($roleGroupId == 6) { // 业务员角色
                        $agentId = \app\admin\library\SqlFilter::getCurrentAgentId();
                        if ($agentId) {
                            $query->where('p.agent_id', $agentId);
                        }
                    }
                }

                // 执行查询
                $data = $query->field([
                    'pbl.id',
                    'pbl.player_id',
                    'pbl.amount',
                    'pbl.balance_before',
                    'pbl.balance_after',
                    'pbl.balance_type',
                    'pbl.transaction_type',
                    'pbct.name as transaction_type_text',
                    'pbl.remark',
                    'pbl.created_at'
                ])
                ->order('pbl.created_at', 'desc')
                ->select();

                // 处理数据
                foreach ($data as &$item) {
                    // 格式化余额类型
                    $item['balance_type_text'] = $item['balance_type'] == 1 ? __('Account balance') : __('Reward balance');
                }

                // 将数据分配给视图
                $this->view->assign('coinLogData', $data);
                $this->view->assign('selectedTransactionType', $transaction_type);
            } catch (\Exception $e) {
                // 记录错误日志
                Log::error('获取玩家金币日志失败: ' . $e->getMessage());
                // 分配空数据
                $this->view->assign('coinLogData', []);
                $this->view->assign('selectedTransactionType', $transaction_type);
            }
        }

        if($name == 'code_amount'){
            try {
                // 调用私有方法获取打码量数据
                $codeAmountData = $this->_getCodeAmountData($player_id);

                // 将数据分配给视图
                $this->view->assign('codeAmountData', $codeAmountData);
                $this->view->assign('playerInfo', $codeAmountData['playerInfo']);

                // 记录日志
                Log::info('加载打码量标签页: 玩家ID=' . $player_id);
            } catch (\Exception $e) {
                // 记录错误日志
                Log::error('获取打码量数据失败: ' . $e->getMessage());

                // 分配空数据
                $this->view->assign('codeAmountData', ['total' => 0, 'rows' => []]);
                $this->view->assign('playerInfo', [
                    'player_id' => $player_id,
                    'player_username' => '--',
                    'player_balance' => '0.00',
                    'channel_name' => '--',
                    'required_betting_amount' => '0.00',
                    'completed_betting_amount' => '0.00',
                    'remaining_amount' => '0.00',
                    'completion_percentage' => '0.00%',
                    'status' => '无数据'
                ]);
            }
        }

        // 其他标签页的特定数据可以在这里加载
        // 注意：大多数标签页的数据加载逻辑已经移到了各自的HTML文件中，通过AJAX请求获取数据

        // 返回标签页内容
        return $this->view->fetch($viewFile);
    }

    /**
     * 玩家详情页(带标签页)
     */
    public function player_detail($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        // 确保日期字段是时间戳格式
        if (isset($row['created_at']) && !is_numeric($row['created_at'])) {
            $row['created_at'] = strtotime($row['created_at']);
        }
        if (isset($row['last_login_time']) && !is_numeric($row['last_login_time'])) {
            $row['last_login_time'] = strtotime($row['last_login_time']);
        }

        // 获取玩家所属渠道信息
        if ($row->agent_id) {
            $agent = Db::table('agents')->where('id', $row->agent_id)->find();
            if ($agent && isset($agent['channel_id'])) {
                $channel = Db::table('channels')->where('id', $agent['channel_id'])->find();
                if ($channel) {
                    $row->channel_id = $channel['id'];
                    // 直接使用渠道名称
                    $row->channel_name = $channel['name'] . '(' . $channel['id'] . ')';
                }
            }
        }

        // 分配玩家数据到视图
        $this->view->assign('row', $row);

        // 获取玩家账户余额等基本信息
        $this->view->assign('balance', $row->balance);
        $this->view->assign('player_id', $row->id);
        $this->view->assign('username', $row->username);

        // 获取玩家提款绑定信息
        $withdrawAccount = Db::table('player_withdraw_accounts')
            ->where('player_id', $row->id)
            ->find();
        $this->view->assign('withdraw_account', $withdrawAccount);

        // 获取玩家佣金配置
        $commissionConfig = \app\admin\model\commission\PlayerCommissionConfigs::getPlayerConfig($row->id);
        $row->commission_config = $commissionConfig;

        return $this->view->fetch();
    }

    /**
     * 获取玩家打码变化数据
     */
    public function get_code_change()
    {
        $player_id = $this->request->get('player_id');
        if (!$player_id) {
            $this->error('参数错误');
        }

        // 这里应该是查询打码变化数据的代码
        // 为了演示，我们返回一些模拟数据
        $data = [];

        // 返回JSON数据
        return json(['rows' => $data, 'total' => count($data)]);
    }

    /**
     * 获取玩家打码量数据（私有方法）
     *
     * @param int $player_id 玩家ID
     * @return array 打码量数据
     */
    private function _getCodeAmountData($player_id)
    {
        // 查询玩家基本信息
        $player = Db::table('players')
            ->alias('p')
            ->join(['channels' => 'c'], 'p.channel_id = c.id', 'LEFT')
            ->where('p.id', $player_id)
            ->field([
                'p.id',
                'p.username',
                'p.balance',
                'p.channel_id',
                'c.name as channel_name'
            ])
            ->find();

        // 格式化玩家信息
        $playerInfo = [
            'player_id' => $player_id,
            'player_username' => $player ? $player['username'] : '--',
            'player_balance' => $player ? number_format($player['balance'], 2) : '0.00',
            'channel_name' => $player && $player['channel_id'] && isset($player['channel_name']) ?
                $player['channel_name'] . '(' . $player['channel_id'] . ')' : '--'
        ];

        // 查询玩家打码任务数据 - 只查询一次
        $tasks = Db::table('player_betting_tasks')
            ->where('player_id', $player_id)
            ->select();

        // 处理每条记录，添加计算字段
        $rows = [];
        foreach ($tasks as $task) {
            $requiredAmount = floatval($task['required_betting_amount']);
            $completedAmount = floatval($task['completed_betting_amount']);

            // 计算剩余打码量和完成百分比
            $remainingAmount = max(0, $requiredAmount - $completedAmount);
            $completionPercentage = $requiredAmount > 0 ?
                min(100, round(($completedAmount / $requiredAmount) * 100, 2)) : 0;

            // 判断是否已完成
            $isCompleted = $completedAmount >= $requiredAmount;
            $statusText = $isCompleted ? __('Completed') : __('Incomplete');

            // 添加计算字段到记录中
            $task['remaining_amount'] = number_format($remainingAmount, 2);
            $task['completion_percentage'] = $completionPercentage . '%';
            $task['status'] = $statusText;
            $task['is_completed'] = $isCompleted;

            $rows[] = $task;
        }

        // 使用第一条记录作为当前任务（如果存在）
        if (!empty($tasks)) {
            $bettingTask = $tasks[0];
            $requiredAmount = floatval($bettingTask['required_betting_amount']);
            $completedAmount = floatval($bettingTask['completed_betting_amount']);

            // 计算剩余打码量和完成百分比
            $remainingAmount = max(0, $requiredAmount - $completedAmount);
            $completionPercentage = $requiredAmount > 0 ?
                min(100, round(($completedAmount / $requiredAmount) * 100, 2)) : 0;

            // 判断是否已完成
            $isCompleted = $completedAmount >= $requiredAmount;
            $statusText = $isCompleted ? __('Completed') : __('Incomplete');

            // 添加到玩家信息中
            $playerInfo['required_betting_amount'] = number_format($requiredAmount, 2);
            $playerInfo['completed_betting_amount'] = number_format($completedAmount, 2);
            $playerInfo['remaining_amount'] = number_format($remainingAmount, 2);
            $playerInfo['completion_percentage'] = $completionPercentage . '%';
            $playerInfo['status'] = $statusText;
            $playerInfo['is_completed'] = $isCompleted;
        } else {
            $playerInfo['required_betting_amount'] = '0.00';
            $playerInfo['completed_betting_amount'] = '0.00';
            $playerInfo['remaining_amount'] = '0.00';
            $playerInfo['completion_percentage'] = '0.00%';
            $playerInfo['status'] = __('No task');
            $playerInfo['is_completed'] = false;
        }

        // 记录日志
        Log::info('获取玩家打码量数据: 玩家ID=' . $player_id);

        // 返回数据
        return [
            'total' => count($rows),
            'rows' => $rows,
            'playerInfo' => $playerInfo
        ];
    }

    /**
     * 获取玩家打码量数据（AJAX接口）
     */
    public function get_code_amount()
    {
        $player_id = $this->request->get('player_id');
        if (!$player_id) {
            $this->error('参数错误');
        }

        try {
            // 调用私有方法获取打码量数据
            $data = $this->_getCodeAmountData($player_id);

            // 返回JSON数据
            return json($data);
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('获取玩家打码量数据失败: ' . $e->getMessage());
            // 返回错误信息
            return json([
                'total' => 0,
                'rows' => [],
                'playerInfo' => [
                    'player_id' => $player_id,
                    'player_username' => '--',
                    'player_balance' => '0.00',
                    'channel_name' => '--',
                    'required_betting_amount' => '0.00',
                    'completed_betting_amount' => '0.00',
                    'remaining_amount' => '0.00',
                    'completion_percentage' => '0.00%',
                    'status' => '无数据'
                ]
            ]);
        }
    }

    /**
     * 获取同IP玩家列表
     */

    /**
     * 获取玩家充值记录
     */
    public function get_deposit()
    {
        $player_id = $this->request->get('player_id');
        if (!$player_id) {
            $this->error('参数错误');
        }

        // 查询充值记录数据
        try {
            $query = Db::table('deposit_orders')
                ->alias('d')
                ->join(['payment_channels'=>'p'], 'd.channel_code = p.code', 'left')
                ->where('d.player_id', $player_id);

            // 获取当前用户角色组ID
            $roleGroupId = \app\admin\library\SqlFilter::getCurrentRoleGroupId();

            // 根据角色类型应用不同的过滤条件
            if ($roleGroupId == 4 || $roleGroupId == 6) {
                // 关联players表以便过滤
                $query->join(['players'=>'pl'], 'd.player_id = pl.id', 'left');

                if ($roleGroupId == 4) { // 渠道角色
                    $channelId = \app\admin\library\SqlFilter::getCurrentChannelId();
                    if ($channelId) {
                        $query->where('pl.channel_id', $channelId);
                    }
                } elseif ($roleGroupId == 6) { // 业务员角色
                    $agentId = \app\admin\library\SqlFilter::getCurrentAgentId();
                    if ($agentId) {
                        $query->where('pl.agent_id', $agentId);
                    }
                }
            }

            $data = $query->field('d.*, p.name as channel_name')
                ->order('d.paid_at desc')
                ->select();

            // 处理数据，添加状态文本
            foreach ($data as &$item) {
                switch ($item['payment_status']) {
                    case 0:
                        $item['status_text'] = '未支付';
                        break;
                    case 1:
                        $item['status_text'] = '已支付';
                        break;
                }
            }

            // 返回JSON数据
            return json(['rows' => $data, 'total' => count($data)]);
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('获取玩家充值记录失败: ' . $e->getMessage());
            // 返回空数据
            return json(['rows' => [], 'total' => 0]);
        }
    }

    /**
     * 获取玩家提现记录
     */
    public function get_withdraw()
    {
        $player_id = $this->request->get('player_id');
        if (!$player_id) {
            $this->error('参数错误');
        }

        // 查询提现记录数据
        try {
            $query = Db::table('withdraw_orders')
                ->alias('wo') // 给 withdraw_orders 表设置别名
                ->join(['player_withdraw_accounts'=>'pwa'], 'wo.player_id = pwa.player_id', 'LEFT') // 左连接 player_withdraw_accounts 表，使用正确的关联字段
                ->where('wo.player_id', $player_id)
                ->field([
                    'wo.id', // 提现订单ID
                    'wo.third_order_no', // 第三方订单号
                    'wo.amount', // 金额
                    'wo.audit_status', // 审核状态
                    'wo.created_at', // 申请时间 (即 withdraw_orders.created_at)
                    'pwa.account_type', // 账户类型
                    'pwa.account_number', // 账户号码
                    'pwa.account_name as account_holder_name', // 使用 account_name 并别名为 account_holder_name 以兼容前端之前的字段名，或直接在前端使用 account_name
                    'pwa.pix_number' // 添加 PIX 号
                ]); // 选择需要的字段

            $data = $query->order('wo.created_at desc') // 使用别名 wo.created_at
                ->select();

            // 定义状态文本映射
            $statusMap = [
                0 => '待加入工单',
                1 => '已加入工单,待审核',
                2 => '已审核',
                3 => '三方处理中',
                4 => '订单完成',
                5 => '拒绝并退回金币',
                6 => '处理失败并退回金币',
                7 => '拒绝并没收金币',
                8 => '虚拟支付'
            ];

            // 处理数据
            foreach ($data as &$item) {
                // 设置状态文本
                $item['status'] = isset($statusMap[$item['audit_status']]) ? $statusMap[$item['audit_status']] : '未知';

                // 设置申请时间和订单号 (这些在field中已经明确指定)
                $item['apply_time'] = $item['created_at'];

                // 格式化账户信息，避免null等问题
                $item['account_type'] = $item['account_type'] ?? 'N/A';
                $item['account_number'] = $item['account_number'] ?? 'N/A';
                // $item['bank_name'] = $item['bank_name'] ?? 'N/A'; // bank_name 字段不存在
                $item['account_holder_name'] = $item['account_holder_name'] ?? 'N/A'; // 此处依赖于 SELECT 中的别名
                $item['pix_number'] = $item['pix_number'] ?? 'N/A'; // 处理 PIX 号
            }

            // 返回JSON数据
            return json(['rows' => $data, 'total' => count($data)]);
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('获取玩家提现记录失败: ' . $e->getMessage());
            // 返回空数据
            return json(['rows' => [], 'total' => 0]);
        }
    }

    /**
     * 获取玩家佣金记录
     */
    public function get_commission()
    {
        $player_id = $this->request->get('player_id');
        if (!$player_id) {
            $this->error('参数错误');
        }

        // 这里应该是查询佣金记录数据的代码
        // 为了演示，我们返回一些模拟数据
        $data = [
            [
                'id' => 1,
                'player_id' => $player_id,
                'commission_type' => '直属佣金',
                'amount' => '100.00',
                'calculate_time' => date('Y-m-d H:i:s', time() - 86400 * 2),
                'issue_time' => date('Y-m-d H:i:s', time() - 86400),
                'status' => '已发放'
            ]
        ];

        // 返回JSON数据
        return json(['rows' => $data, 'total' => count($data)]);
    }

    /**
     * 获取玩家下级数据
     */
    public function get_children()
    {
        $player_id = $this->request->get('player_id');
        if (!$player_id) {
            $this->error('参数错误');
        }

        try {
            // 查询玩家信息
            $player = \app\admin\model\players\Players::get($player_id);
            if (!$player) {
                $this->error('玩家不存在');
            }

            // 获取当前用户角色组ID
            $roleGroupId = \app\admin\library\SqlFilter::getCurrentRoleGroupId();

            // 根据角色类型应用不同的过滤条件
            $whereCondition = [];
            if ($roleGroupId == 4) { // 渠道角色
                $channelId = \app\admin\library\SqlFilter::getCurrentChannelId();
                if ($channelId) {
                    $whereCondition['channel_id'] = $channelId;
                }
            } elseif ($roleGroupId == 6) { // 业务员角色
                $agentId = \app\admin\library\SqlFilter::getCurrentAgentId();
                if ($agentId) {
                    $whereCondition['agent_id'] = $agentId;
                }
            }

            // 查询一级下线数量
            $level1Count = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 1)
                ->where($whereCondition)
                ->count();

            // 查询二级下线数量
            $level2Count = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 2)
                ->where($whereCondition)
                ->count();

            // 查询三级下线数量
            $level3Count = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pr.relation_level', 3)
                ->where($whereCondition)
                ->count();

            // 计算总下线数量
            $totalCount = $level1Count + $level2Count + $level3Count;

            // 查询团队总充值金额
            $teamDeposit = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['deposit_orders' => 'd'], 'pr.player_id = d.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('d.payment_status', 1) // 已支付的订单
                ->where($whereCondition)
                ->sum('d.amount');

            // 查询团队总提现金额
            $teamWithdraw = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['withdraw_orders' => 'w'], 'pr.player_id = w.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('w.audit_status', 'in', [4, 8]) // 已完成或虚拟支付的订单
                ->where($whereCondition)
                ->sum('w.amount');

            // 计算团队充提差
            $teamDiff = $teamDeposit - $teamWithdraw;

            // 查询有充值记录的下线数量
            $depositCount = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['deposit_orders' => 'd'], 'pr.player_id = d.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('d.payment_status', 1) // 已支付的订单
                ->where($whereCondition)
                ->group('pr.player_id')
                ->count();

            // 查询活跃玩家数量（最近7天有登录记录的玩家）
            $activeCount = Db::table('player_relations')
                ->alias('pr')
                ->join(['players' => 'p'], 'pr.player_id = p.id', 'LEFT')
                ->join(['player_login_logs' => 'pll'], 'pr.player_id = pll.player_id', 'LEFT')
                ->where('pr.ancestor_id', $player_id)
                ->where('pll.created_at', '>=', date('Y-m-d H:i:s', strtotime('-7 days')))
                ->where($whereCondition)
                ->group('pr.player_id')
                ->count();

            // 计算充值率
            $depositRate = $totalCount > 0 ? number_format(($depositCount / $totalCount) * 100, 2) : 0;

            // 组装返回数据
            $data = [
                'code' => 1,
                'msg' => '获取成功',
                'team_deposit' => number_format($teamDeposit, 2, '.', ''),
                'team_withdraw' => number_format($teamWithdraw, 2, '.', ''),
                'team_diff' => number_format($teamDiff, 2, '.', ''),
                'level1_count' => $level1Count,
                'level2_count' => $level2Count,
                'level3_count' => $level3Count,
                'total_count' => $totalCount,
                'active_count' => $activeCount,
                'deposit_count' => $depositCount,
                'deposit_rate' => $depositRate . '%'
            ];

            // 返回JSON数据
            return json($data);
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('获取玩家下级数据失败: ' . $e->getMessage());
            // 返回错误信息
            $this->error('获取数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新玩家标签
     */
    public function update_tag()
    {
        $player_id = $this->request->post('player_id');
        $tag_id = $this->request->post('tag_id');

        if (!$player_id || !$tag_id) {
            $this->error(__('Parameter %s can not be empty', 'player_id/tag_id'));
        }

        $result = \app\admin\model\players\Players::updatePlayerTag($player_id, $tag_id);

        if ($result) {
            // 获取新的标签名称
            $tag = \app\admin\model\players\PlayerTags::get($tag_id);
            $tag_name = $tag ? $tag['name'] : '';
            $this->success('', null, ['tag_name' => $tag_name]);
        } else {
            $this->error(__('Operation failed'));
        }
    }

    /**
     * 直接登录前台
     * 以玩家身份登录到前台
     */
    public function direct_login()
    {
        $player_id = $this->request->param('player_id');
        if (!$player_id) {
            $this->error(__('Parameter %s can not be empty', 'player_id'));
        }

        // 检查玩家是否存在
        $player = $this->model->find($player_id);
        if (!$player) {
            $this->error(__('Player does not exist'));
        }

        // 记录管理员操作日志
        Log::info('管理员(' . $this->auth->id . ')以玩家身份直接登录: player_id=' . $player_id . ', phone_number=' . $player['phone_number']);

        // 使用工具函数获取清理后的域名
        $domain = get_clean_domain();

        // 生成一个唯一的令牌，用于跨域登录验证
        $token = md5(uniqid(mt_rand(), true));

        // 将玩家信息和令牌存储在缓存中，设置较短的过期时间（例如5分钟）
        \think\Cache::set('direct_login_' . $token, [
            'player_id' => $player['id'],
            'phone_number' => $player['phone_number'],
            'time' => time()
        ], 300);

        // 获取前台首页URL，并附加令牌参数
        $frontendUrl = $domain . '?direct_login_token=' . $token;

        // 记录日志
        Log::info('生成直接登录链接: token=' . $token . ', url=' . $frontendUrl);

        // 返回成功信息和前台URL
        $this->success('准备以玩家身份登录', null, ['url' => $frontendUrl]);
    }

    /**
     * 获取玩家游戏数据
     */
    public function get_game_data()
    {
        $player_id = $this->request->get('player_id');
        if (!$player_id) {
            $this->error('参数错误');
        }

        // 获取筛选参数
        $game_type = $this->request->get('game_type', '');
        $start_time = $this->request->get('start_time', '');
        $end_time = $this->request->get('end_time', '');

        // 查询游戏记录数据
        try {
            // 构建查询
            $query = Db::table('game_sessions')
                ->alias('gs')
                ->join(['game_rooms' => 'room'], 'gs.game_id = room.game_room_id', 'LEFT')
                ->where('gs.player_id', $player_id);

            // 应用筛选条件
            if (!empty($game_type)) {
                // 根据游戏类型筛选 - 保留筛选功能但不显示类型列
                $query->join(['game_providers' => 'gp'], 'gs.provider_id = gp.id', 'LEFT');
                $query->where('gp.type', $game_type);
            }

            if (!empty($start_time)) {
                $query->where('gs.created_at', '>=', $start_time);
            }

            if (!empty($end_time)) {
                $query->where('gs.created_at', '<=', $end_time);
            }

            // 获取当前用户角色组ID
            $roleGroupId = \app\admin\library\SqlFilter::getCurrentRoleGroupId();

            // 根据角色类型应用不同的过滤条件
            if ($roleGroupId == 4) { // 渠道角色
                $channelId = \app\admin\library\SqlFilter::getCurrentChannelId();
                if ($channelId) {
                    // 关联players表以便过滤
                    $query->join(['players'=>'p'], 'gs.player_id = p.id', 'left');

                    $query->where('p.channel_id', $channelId);
                }
            } elseif ($roleGroupId == 6) { // 业务员角色
                $agentId = \app\admin\library\SqlFilter::getCurrentAgentId();
                if ($agentId) {
                    // 关联players表以便过滤
                    $query->join(['players'=>'p'], 'gs.player_id = p.id', 'left');

                    $query->where('p.agent_id', $agentId);
                }
            }

            // 执行查询
            $data = $query->field([
                'gs.session_id as id',
                'gs.game_id',
                'room.name as game_name',
                'gs.bet_amounts as bet_amount',
                'gs.win_amounts as win_amount',
                '(gs.win_amounts - gs.bet_amounts) as profit',
                'gs.created_at as start_time'
            ])
            ->order('gs.created_at', 'desc')
            ->select();

            // 格式化数据
            foreach ($data as &$item) {
                // 格式化数值
                $item['bet_amount'] = number_format($item['bet_amount'], 2);
                $item['win_amount'] = number_format($item['win_amount'], 2);
                $item['profit'] = number_format($item['profit'], 2);

                // 格式化开始时间
                if (!empty($item['start_time'])) {
                    $item['start_time'] = date('Y-m-d H:i:s', strtotime($item['start_time']));
                }
            }

            // 返回JSON数据
            return json(['rows' => $data, 'total' => count($data)]);
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('获取玩家游戏数据失败: ' . $e->getMessage());
            // 返回空数据
            return json(['rows' => [], 'total' => 0]);
        }
    }

    /**
     * 修改玩家密码 (客服操作)
     */
    public function update_password()
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid request'));
        }

        $player_id = $this->request->post('player_id');
        $password = $this->request->post('password');

        if (!$player_id || !$password) {
            $this->error(__('Parameter %s can not be empty', 'player_id/password'));
        }

        // Find the player
        $player = $this->model->find($player_id);
        if (!$player) {
            $this->error(__('Player does not exist'));
        }

        // 直接使用原始密码值，不进行哈希处理
        $rawPassword = $password;

        Db::startTrans();
        try {
            // Update player password
            $player->password = $rawPassword;
            $player->save();

            Db::commit();
            $this->success(__('Password updated successfully'));

        } catch (ValidateException $e) {
            Db::rollback();
            $this->error($e->getMessage());
        } catch (PDOException $e) {
            Db::rollback();
            $this->error($e->getMessage());
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }
}
